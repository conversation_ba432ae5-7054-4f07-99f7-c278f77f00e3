import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { visionAI, textAI } from '@/lib/ai';
import { notionService } from '@/lib/notion';
import sharp from 'sharp';
import fs from 'fs';
import path from 'path';

// 保存图片到服务器（临时）
async function saveImageToServer(buffer: Buffer, filename: string, request: NextRequest): Promise<{ url: string; filePath: string }> {
  try {
    let actualUploadsDir: string;
    let usePublicPath = true;

    // 首先尝试 public/uploads 目录
    const publicUploadsDir = path.join(process.cwd(), 'public', 'uploads');
    console.log('尝试创建目录:', publicUploadsDir);

    try {
      if (!fs.existsSync(publicUploadsDir)) {
        fs.mkdirSync(publicUploadsDir, { recursive: true });
      }
      actualUploadsDir = publicUploadsDir;
      console.log('成功使用 public/uploads 目录');
    } catch (mkdirError) {
      console.error('创建 public/uploads 目录失败:', mkdirError);

      // 尝试使用备用目录
      const fallbackDir = path.join(process.cwd(), 'uploads');
      console.log('尝试使用备用目录:', fallbackDir);

      try {
        if (!fs.existsSync(fallbackDir)) {
          fs.mkdirSync(fallbackDir, { recursive: true });
        }
        actualUploadsDir = fallbackDir;
        usePublicPath = false;
        console.log('成功使用备用 uploads 目录');
      } catch (fallbackError) {
        console.error('创建备用目录也失败:', fallbackError);
        throw new Error('无法创建上传目录');
      }
    }

    // 生成唯一文件名
    const timestamp = Date.now();
    const ext = path.extname(filename) || '.jpg';
    const uniqueFilename = `${timestamp}-${Math.random().toString(36).substring(2)}${ext}`;
    const filePath = path.join(actualUploadsDir, uniqueFilename);

    // 保存文件
    fs.writeFileSync(filePath, buffer);
    console.log('文件保存成功:', filePath);

    // 生成URL
    const protocol = request.headers.get('x-forwarded-proto') || 'http';
    const host = request.headers.get('host') || 'localhost:3000';
    const url = `${protocol}://${host}/uploads/${uniqueFilename}`;
    return { url, filePath };
  } catch (error) {
    console.error('保存图片失败:', error);
    throw new Error('保存图片失败');
  }
}

// 延迟删除文件
function scheduleFileDelete(filePath: string, delayMs: number = 300000) { // 5分钟后删除
  setTimeout(() => {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log('临时图片文件已删除:', filePath);
      }
    } catch (error) {
      console.error('删除临时文件失败:', error);
    }
  }, delayMs);
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    requireAuth(request);

    const formData = await request.formData();
    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json({
        success: false,
        message: '未找到上传的图片'
      }, { status: 400 });
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({
        success: false,
        message: '只支持图片文件'
      }, { status: 400 });
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({
        success: false,
        message: '图片文件过大，请选择小于10MB的图片'
      }, { status: 400 });
    }

    // 读取文件内容
    const buffer = Buffer.from(await file.arrayBuffer());

    // 使用 Sharp 处理图片
    const processedImage = await sharp(buffer)
      .resize(1024, 1024, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ quality: 80 })
      .toBuffer();

    // 转换为 base64
    const base64Image = processedImage.toString('base64');

    // 保存图片到服务器（临时）
    const { url: imageUrl, filePath } = await saveImageToServer(processedImage, file.name, request);

    // AI 分析图片
    const analysisResult = await visionAI.analyzeImage(base64Image);

    if (!analysisResult.success) {
      return NextResponse.json({
        success: false,
        message: analysisResult.message || 'AI 分析失败'
      }, { status: 500 });
    }

    const { analysis } = analysisResult;

    // 验证和映射科目名称
    const validSubjects = ['数学', '英语', '政治', '计算机408'];
    let mappedSubject = '数学'; // 默认科目

    if (analysis.subject) {
      // 尝试匹配科目
      const subjectLower = analysis.subject.toLowerCase();
      const subjectOriginal = analysis.subject;

      // 直接匹配
      if (validSubjects.includes(subjectOriginal)) {
        mappedSubject = subjectOriginal;
      }
      // 模糊匹配
      else if (subjectLower.includes('数学') || subjectLower.includes('math') || subjectLower.includes('mathematics')) {
        mappedSubject = '数学';
      } else if (subjectLower.includes('英语') || subjectLower.includes('english') || subjectLower.includes('英文')) {
        mappedSubject = '英语';
      } else if (subjectLower.includes('政治') || subjectLower.includes('politics') || subjectLower.includes('政治学')) {
        mappedSubject = '政治';
      } else if (subjectLower.includes('计算机') || subjectLower.includes('408') || subjectLower.includes('computer') || subjectLower.includes('编程') || subjectLower.includes('程序')) {
        mappedSubject = '计算机408';
      }
      // 如果是"未知"或其他无法识别的，根据内容进一步判断
      else if (subjectOriginal === '未知' || subjectLower.includes('unknown')) {
        const content = (analysis.content || '').toLowerCase();
        if (content.includes('数学') || content.includes('math') || content.includes('公式') || content.includes('计算')) {
          mappedSubject = '数学';
        } else if (content.includes('英语') || content.includes('english') || content.includes('单词') || content.includes('语法')) {
          mappedSubject = '英语';
        } else if (content.includes('政治') || content.includes('马克思') || content.includes('毛概')) {
          mappedSubject = '政治';
        } else if (content.includes('计算机') || content.includes('编程') || content.includes('代码') || content.includes('算法')) {
          mappedSubject = '计算机408';
        }
      }
    }

    console.log(`科目映射: ${analysis.subject} -> ${mappedSubject}`);

    // 将映射后的科目添加到分析结果中
    const analysisWithMappedSubject = {
      ...analysis,
      mappedSubject: mappedSubject
    };

    // 生成简要记录
    console.log('开始生成简要记录...');
    const briefResult = await textAI.generateBriefRecord(analysisWithMappedSubject);
    console.log('简要记录结果:', briefResult);
    const briefRecord = briefResult.success ? briefResult.briefRecord : '学习记录';

    // 让文本AI重新分析原始结果
    console.log('开始文本AI重新分析...');
    const formatResult = await textAI.formatDetailedAnalysis(analysis);
    console.log('文本AI分析结果:', formatResult);
    const formattedContent = formatResult.success ? formatResult.formattedContent : (analysis.content || '图片内容分析');

    // 保存简要记录到科目单元格 - 使用append模式，用逗号分隔
    const saveResult = await notionService.updateStudyRecord(
      mappedSubject,
      briefRecord || '学习记录',
      'append'
    );

    // 保存详细内容到页面
    console.log('保存到页面的内容:', formattedContent);
    const pageResult = await notionService.updatePageContent(
      formattedContent,
      imageUrl
    );

    if (!saveResult.success) {
      console.error('保存到 Notion 失败:', saveResult.message);
      return NextResponse.json({
        success: false,
        message: `保存到 Notion 失败: ${saveResult.message || '未知错误'}`,
        debug: {
          subject: mappedSubject,
          briefRecord: briefRecord,
          contentLength: (analysis.content || '图片内容分析').length
        }
      }, { status: 500 });
    }

    // 检查页面更新结果（非关键错误）
    if (!pageResult.success) {
      console.warn('更新页面内容失败:', pageResult.message);
    }

    // 安排5分钟后删除临时图片文件
    scheduleFileDelete(filePath);

    return NextResponse.json({
      success: true,
      message: '图片上传和分析成功',
      data: {
        analysis: {
          ...analysis,
          briefRecord: briefRecord,
          mappedSubject: mappedSubject
        },
        saved: true,
        pageUpdated: pageResult.success
      }
    });

  } catch (error) {
    console.error('图片上传处理错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
