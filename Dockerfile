# 多阶段构建 Dockerfile for 学习管理系统 2.0

# 第一阶段：依赖安装和构建
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖（用于 sharp 等原生模块）
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++

# 复制 package 文件
COPY package*.json ./

# 安装所有依赖（包括devDependencies，构建需要）
RUN npm ci && npm cache clean --force

# 复制源代码
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 构建应用（跳过类型检查）
RUN npm run build

# 第二阶段：运行时镜像
FROM node:20-alpine AS runner

# 安装运行时依赖
RUN apk add --no-cache \
    curl \
    dumb-init

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=5002
ENV HOSTNAME=0.0.0.0

# 从构建阶段复制必要文件
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 创建上传和日志目录，确保权限正确
RUN mkdir -p public/uploads uploads logs && \
    chown -R nextjs:nodejs public uploads logs

# 切换到非 root 用户
USER nextjs

# 暴露端口
EXPOSE 5002

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:5002/api/health || exit 1

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]